import os
from openpyxl import load_workbook
from PIL import Image
from io import BytesIO

# مسار ملف الإكسل
excel_file = "book1.xlsx"  # قم بتغييره إلى اسم ملفك
output_dir = "exported_images"

# إنشاء مجلد لحفظ الصور
os.makedirs(output_dir, exist_ok=True)

# تحميل ملف الإكسل
wb = load_workbook(excel_file)
sheet = wb.active

# الحصول على الصور من الملف
for img in sheet._images:
    # الصورة كمصفوفة بايت
    img_bytes = img.ref if hasattr(img, 'ref') else None
    img_data = img._data()

    # تحديد الخلية المرتبطة بالصورة
    anchor = img.anchor._from
    row = anchor.row

    # استخراج الكود من العمود الأول (A مثلاً)
    code_cell = f"A{row+1}"  # لأن Excel 1-based وopenpyxl 0-based
    product_code = sheet[code_cell].value

    if product_code:
        try:
            # فتح الصورة باستخدام PIL
            image = Image.open(BytesIO(img_data))
            image = image.convert("RGB")  # تأكد أنها ليست بصيغة PNG شفافة

            # حفظ الصورة بصيغة WebP
            output_path = os.path.join(output_dir, f"{product_code.replace(' ', '_')}.webp")
            image.save(output_path, "WEBP", quality=95)  # الجودة 95%

            print(f"تم حفظ الصورة: {output_path}")
        except Exception as e:
            print(f"خطأ أثناء حفظ الصورة للكود {product_code}: {e}")
    else:
        print(f"تعذر العثور على الكود في الصف {row+1}")
